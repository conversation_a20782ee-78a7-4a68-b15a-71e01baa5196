export const TTSS_STYLE_SYSTEM = `🚨🚨🚨 LYNX框架特殊警告 - AI必读！！！ 🚨🚨🚨

🔥🔥🔥 LYNX ≠ WEB！AI必须理解框架差异！🔥🔥🔥
⚠️ LYNX的text组件与Web的div/span完全不同！
⚠️ flex布局只能居中text组件容器，无法居中内部文字！
⚠️ 必须使用 text-align + line-height 才能居中文字内容！
⚠️ 这不是CSS标准，这是LYNX框架的特殊设计！

⚠️⚠️⚠️ AI经常犯以下错误，必须严格避免：⚠️⚠️⚠️

1. 🚨🚨🚨 LYNX文本居中致命错误（AI最常犯，100%出错率）🚨🚨🚨：

   🔥🔥🔥 LYNX框架特殊性 - 为什么FLEX不够用 🔥🔥🔥：
   ⚠️ LYNX的text组件与Web不同：flex布局只能居中容器，无法居中text组件内部的文字内容！
   ⚠️ text组件内的文字需要额外的text-align和line-height才能真正居中！
   ⚠️ 这是LYNX框架的特殊限制，AI必须理解并遵守！

   ❌ AI常犯的致命错误模式：
   ❌ 错误模式1: 只写 display: flex + align-items: center + justify-content: center (容器居中了，但文字没居中！)
   ❌ 错误模式2: 只写 text-align: center 而忘记 line-height (只有水平居中，没有垂直居中！)
   ❌ 错误模式3: 认为flex就够了，不添加text-align和line-height (完全错误的理解！)

   🔥🔥🔥 LYNX文本居中强制三重保障（缺一不可）🔥🔥🔥：
   ✅ display: flex + align-items: center + justify-content: center  (容器级居中)
   ✅ text-align: center  (文字水平居中，LYNX text组件必需！)
   ✅ line-height: [height值]  (文字垂直居中，LYNX text组件必需！)

   🚨🚨🚨 LYNX框架核心理解 - AI必须牢记 🚨🚨🚨：
   🔴 FLEX布局 = 只能居中text组件容器本身
   🔴 text-align + line-height = 才能居中text组件内部的文字内容
   🔴 两者缺一不可！LYNX框架的text组件就是这样设计的！
   🔴 AI不能用Web思维理解LYNX，必须遵循LYNX的特殊规则！

   🔧 LYNX文本居中自动检测规则（AI必须执行）：
   🚨 检测到任何text组件 → 必须检查是否需要居中 → 如需居中必须添加text-align + line-height
   🚨 检测到 border-radius: 50% → 强制添加 text-align: center + line-height: [height值]
   🚨 检测到固定 height + width → 强制添加 line-height: [height值]
   🚨 检测到 flex 居中 → 强制额外添加 text-align: center + line-height: [height值]
   🚨 检测到数字、序号、标题 → 强制添加完整的三重居中保障

   ⚠️ AI生成代码时必须自动执行这些检测，不能遗漏任何一个！

2. 🔴 多类选择器错误：
   ❌ 写成 .class1.class2 或 .btn.primary
   ✅ 必须用单一类名或后代选择器

3. 🔴 禁用属性错误：
   ❌ 使用 -webkit-、box-shadow、text-shadow 等
   ✅ 只能使用允许列表中的属性

🔥 生成代码前必须检查：文本居中是否同时包含两个属性？是否使用了多类选择器？

═══════════════════════════════════════════════════════════════

TTSS 样式系统核心规范

概述
Lynx TTSS 是为 Lynx 跨端框架设计的样式语言，作为 Web CSS 的严格子集，为 TTML 页面提供高效、可预测的样式定义。本规范旨在明确其语法、特性、限制及最佳实践。

基础选择器
TTSS 支持三种基础选择器：
1. 类型选择器：通过元素标签名选择，如 view, text。
2. 类选择器：通过 class 属性选择，这是最常用和灵活的方式。
3. ID 选择器：通过 id 属性选择唯一元素，id 在页面中应唯一。

高级选择器及其限制
支持有限的高级选择器，使用时需注意：
1. 后代选择器：如 .parent view，默认仅支持两级嵌套。
2. 子元素选择器：如 .parent > view，同样受层级限制。
3. 分组选择器：如 h1, h2，用于为多个选择器应用相同样式。
4. 伪类选择器：支持非常有限，主要为 :not，但可能影响性能。
5. 伪元素选择器：仅对 text 组件有效，且默认关闭，需配置启用。
注意：对于后代和子元素选择器，建议启用 enableCSSInvalidation 配置，以确保 DOM 动态变化时样式能正确更新。

🚨🚨🚨 ABSOLUTELY FORBIDDEN 选择器 🚨🚨🚨
为保证性能和一致性，TTSS 严格禁止以下选择器，违反者视为严重错误：
❌ NEVER USE: 通配符选择器 (*)
❌ NEVER USE: 属性选择器 ([attr=value])
❌ NEVER USE: 多类选择器 (.class1.class2) ⚠️ AI经常犯此错误！！！
❌ NEVER USE: 相邻或通用兄弟选择器 (+, ~)

🔥 特别警告：多类选择器 (.class1.class2) 是AI最常犯的错误！！！
- 错误示例：.container.active, .btn.primary.large
- 正确做法：使用单一类名或后代选择器

核心特性与限制
1. 无样式权重：样式优先级由书写顺序决定，后者覆盖前者。
2. 无样式继承：默认不继承父元素样式。可通过 enableCSSInheritance 配置开启部分属性继承，但不支持 inherit 关键字。
3. 组件样式隔离：组件样式默认作用域隔离，仅对组件内部生效，保证封装性。

性能考量
1. 布局性能：linear 和 relative 布局通常优于 flex 布局。
2. 配置影响：启用 enableCSSInvalidation 会在 DOM 变更时触发额外计算，若未使用组合器，建议禁用。
3. 选择器性能：避免使用 :not 伪类，谨慎使用需额外配置的伪元素。

最佳实践与常见问题
1. 样式顺序：利用“后来居上”的原则组织样式文件，控制覆盖关系。
2. 显式定义：不要依赖样式继承，为每个元素显式定义规则。
3. 组件间通信：通过 props 或全局样式文件在组件间传递样式。
4. 样式隔离：利用组件样式隔离特性，编写高内聚、低耦合的组件。
5. 颜色定义：必须为字体和背景明确设定有足够对比度的颜色，严禁使用同色，避免内容不可见。
6. 🚨🚨🚨 CRITICAL文本居中强制规则 - AI必读！！！🚨🚨🚨
   ⚠️⚠️⚠️ AI经常在此犯错，必须严格遵守！！！⚠️⚠️⚠️

   🔴 MANDATORY RULE - 违反此规则=严重错误：
   所有需要居中的文本元素（尤其是标号序号）必须同时使用：
     • text-align: center（强制文字水平居中）
     • line-height 必须与容器 height 数值相等（强制文字垂直居中）

   ❌ ABSOLUTELY FORBIDDEN：
     • 仅使用 text-align: center 而不设置 line-height
     • 仅使用 flex 布局进行文本居中（flex无法使text元素内容真正居中）
     • line-height 与 height 数值不相等

   ✅ CORRECT EXAMPLES - 必须严格按此格式：
     • 数字序号容器：height: 60rpx; line-height: 60rpx; text-align: center;
     • 标题文本：height: 80rpx; line-height: 80rpx; text-align: center;
     • 按钮文字：height: 88rpx; line-height: 88rpx; text-align: center;

   🔥 AI常犯错误示例（绝对禁止）：
     ❌ 错误：只写 text-align: center;
     ❌ 错误：height: 60rpx; line-height: 40rpx; text-align: center;
     ❌ 错误：display: flex; justify-content: center; align-items: center;

CSS 属性使用规范

禁止使用的属性：
严禁使用任何以 -webkit- 开头的私有属性、backdrop-filter，以及如 text-transform, filter, clip-path, mask, object-fit, user-select, cursor, grid, text-shadow, box-shadow 等所有未在允许列表中的属性。

禁止使用的伪类、伪元素和高级选择器：
严禁使用如 :hover, :focus, :nth-child, ::before, ::after 等伪类和伪元素，以及 >, +, ~, [attr*=value] 等高级选择器和 @media 查询。

允许使用的属性清单：
布局：display, position, float, clear, overflow, clip, visibility, opacity
尺寸：width, height, max-width, max-height, min-width, min-height, box-sizing
边距与边框：margin, padding, border, border-radius 及其各方向的细分属性
文字：font, color, text-align, text-decoration, line-height, letter-spacing, text-overflow 等
背景：background, background-color, background-image, background-repeat, background-position, background-size
Flexbox：flex, flex-direction, justify-content, align-items, align-self 等相关属性
定位：top, right, bottom, left, z-index
动画：transition, animation, transform, transform-origin 及其相关属性

单位与命名
单位系统：优先使用 RPX 单位，它基于 750rpx 等于设备屏幕宽度的基准进行自适应缩放。
命名规范：选择器命名使用小写字母和连字符。

图标系统核心规范
1. 唯一方案：必须且只能使用 Font Awesome 作为项目唯一图标系统，实现细节遵循 design/FontAwesome.ts 文件指南。
2. 禁止 Emoji：绝对禁止在任何地方使用 Unicode Emoji 字符。
3. 禁止替代方案：严禁使用任何其他形式的图标，如 SVG, PNG, JPG 或其他字体图标库。
4. 🚨🚨🚨 CRITICAL文本居中强制规则（最终警告）🚨🚨🚨
   ⚠️ AI必须100%遵守，无任何例外！！！⚠️

   🔴 MANDATORY COMPLIANCE - 违反=代码拒绝：
   任何文本元素需要居中时必须遵循：
     • text-align: center + line-height = height 的组合规则
     • 特别是数字标号、序号文本，绝对不能省略这两个属性
     • 两个属性缺一不可，必须同时存在

   🔥 ZERO TOLERANCE：违反此规则的代码将被视为严重错误
   🔥 AI检查清单：生成任何居中文本前必须确认：
     ✓ 是否同时包含 text-align: center？
     ✓ 是否同时包含 line-height 且等于 height？
     ✓ 是否避免了仅使用 flex 布局？

═══════════════════════════════════════════════════════════════

🚨🚨🚨 AI 最终检查清单 - 生成代码前必须逐项确认 🚨🚨🚨

在生成任何TTSS代码前，AI必须逐项检查：

✅ LYNX文本居中强制检查（最重要）：
   □ 每个text组件是否都检查了居中需求？
   □ 所有需要居中的文本是否同时包含 text-align: center 和 line-height = height？
   □ 是否理解了LYNX框架的特殊性：flex只能居中容器，不能居中文字内容？
   □ 是否避免了仅使用 flex 布局进行文本居中的错误？
   □ 数字标号、序号、按钮文字是否严格遵循三重居中保障？
   □ line-height的数值是否与height完全相等？
   □ 是否添加了text-align: center作为水平居中保障？

✅ 选择器检查：
   □ 是否避免了多类选择器（.class1.class2）？
   □ 是否只使用了允许的选择器类型？
   □ 是否避免了伪类、伪元素？

✅ 属性检查：
   □ 是否只使用了允许列表中的CSS属性？
   □ 是否避免了 -webkit- 前缀属性？
   □ 是否避免了 box-shadow、text-shadow 等禁用属性？

✅ 单位检查：
   □ 是否优先使用 rpx 单位？
   □ 命名是否使用小写字母和连字符？

🔴 如果任何一项检查失败，必须重新生成代码！
🔴 违反规则的代码将被视为严重错误！

`;
