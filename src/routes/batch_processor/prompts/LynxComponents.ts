export const LYNX_COMPONENTS = `🎯 LYNX COMPONENTS RULES (SOURCE-CODE VERIFIED)

=== R1: COMPONENT RESTRICTIONS (源码: lynx-components/ComponentValidator.ts) ===
RULE: 禁用HTML标签 div,p,img,span,label,button,section,article,ul,ol,li,table
RULE: 必用Lynx组件 view,text,image,scroll-view,input,textarea,picker,canvas,swiper
RULE: 移动端特有组件需注意平台兼容性
RULE: 非Lynx组件必须封装后使用

=== R2: TEXT WRAPPING RULES (源码: lynx-text/TextWrapper.ts) ===
🚨 CRITICAL: 文字和图标强制包裹规则 (重要技术约束)
RULE: 所有文字和图标必须使用 text 标签包裹
RULE: 禁止在 view 或其他容器中放置裸露文字
RULE: 严禁使用 Emoji 字符，只能使用 Font Awesome 图标

=== R2.1: XML ESCAPING RULES (源码: lynx-text/XMLEscaper.ts) ===
🚨 CRITICAL: 精确XML转义规则 (防止模板语法错误)

🔥 转义判断标准：
RULE: 静态文本内容 → 必须转义 < > & " '
RULE: 模板表达式 {{}} → 绝对禁止转义
RULE: 属性值中的 {{}} → 绝对禁止转义

✅ 必须转义的场景 (静态文本中的数学符号):
<text>• |a| &gt; 1 更窄</text>
<text>• |a| &lt; 1 更宽</text>
<text>• a &lt; 0 开口向下</text>
<text>温度 &gt; 30度时需要开空调</text>
<text>价格 &lt; 100元的商品</text>
<text>数学公式：a &gt; b &amp;&amp; b &lt; c</text>

❌ 绝对禁止转义的场景 (动态模板表达式):
<text class="metric-change {{fedData.rateChange > 0 ? 'positive' : 'negative'}}">
<view tt:if="{{temperature > 30}}">
<text>{{price < 100 ? '便宜' : '昂贵'}}</text>
<view bindtap="handleClick" data-value="{{item.id > 0 ? item.id : 0}}">

🎯 关键区分：
- 如果 < > 符号在 {{}} 外面 → 必须转义为 &lt; &gt;
- 如果 < > 符号在 {{}} 里面 → 绝对不能转义，保持原样

🚨 CLAUDE4 最高频致命错误 (必须严格避免) 🚨：

🔥 ERROR #1 - 标签闭合错误 (AI最常犯，100%错误率) 🔥：
❌ 使用 </div> 来闭合 <view> 标签 - 这是AI最常犯的错误！
❌ 使用 </div> 来闭合 <text> 标签
❌ 使用 </div> 来闭合 <scroll-view> 标签
❌ 任何Lynx标签用HTML标签闭合

🔥 ERROR #2 - HTML标签混用 (绝对禁止) 🔥：
1. button - 出现频率极高，必须用 view + bindtap
2. div - HTML思维惯性，必须用 view
3. span - 必须用 text
4. img - 必须用 image (自闭合)
5. ul/li - 必须用 view
6. table/tr/td - 必须用 view
7. p/h1-h6 - 必须用 text
8. 🚨 canvas + lightcharts-canvas 混用 - 致命错误！

❌ 错误示例 (绝对禁止):
<view>这是裸露的文字</view>  <!-- 错误：文字没有包裹 -->
<view>🔥</view>  <!-- 错误：使用了Emoji -->
<view>内容</div>  <!-- 🚨致命错误：用div闭合view标签 -->
<text>文字</div>  <!-- 🚨致命错误：用div闭合text标签 -->
<button bindtap="handleClick">按钮</button>  <!-- 错误：使用HTML标签 -->
<div class="container">内容</div>  <!-- 错误：使用HTML标签 -->
<span>文字</span>  <!-- 错误：使用HTML标签 -->
<img src="image.png" />  <!-- 错误：使用HTML标签 -->

✅ 正确示例:
<view><text>这是正确包裹的文字</text></view>
<view><text>&#xf015;</text></view>  <!-- 正确：Font Awesome图标 -->

=== R3: ICON POSITIONING RULES (源码: lynx-icons/IconPositioner.ts) ===
RULE: 推荐直接使用纯图标，避免背景色块
RULE: 图标居中必须使用 text-align: center
RULE: 完整居中方案 display: flex + align-items: center + justify-content: center
RULE: 避免图标与背景色块组合的定位偏移问题

=== R4: SCROLL VIEW RULES (源码: lynx-scroll/ScrollViewManager.ts) ===
RULE: 所有滚动内容必须使用 scroll-view，禁止 view 滚动
RULE: scroll-view 必须设置 height 或 max-height，禁止 min-height
RULE: 列表渲染、长内容、聊天界面、文章阅读必须使用 scroll-view
RULE: 垂直滚动设置 scroll-y="true"，水平滚动设置 scroll-x="true"

=== R5: TEXT CENTERING RULES (源码: lynx-text/TextCentering.ts) ===
RULE: 文本居中必须同时使用 text-align: center + line-height = height
RULE: 禁止仅依赖 flex 布局居中 text 元素内容
RULE: 数字序号、标题、按钮文字必须严格居中
RULE: 违反居中规则属于严重样式错误

=== R6: BASIC COMPONENTS (源码: lynx-components/BasicComponents.ts) ===
RULE: text - 唯一文字容器，支持换行和样式
RULE: image - 图片组件，自闭合标签，替代HTML img
RULE: video,audio,camera,live-player,live-pusher - 媒体组件
RULE: map - 地图容器，canvas - 2D绘图画布

=== R7: ADVANCED COMPONENTS (源码: lynx-components/AdvancedComponents.ts) ===
RULE: view - 万能容器，支持 flexbox、定位、动画
RULE: scroll-view - 高性能滚动，支持下拉刷新、上拉加载
RULE: swiper - 轮播容器，支持指示器、自动播放、循环
RULE: cover-view - 原生组件覆盖层，解决层级问题

=== R8: CARD STRUCTURE RULES (源码: lynx-cards/CardStructure.ts) ===
RULE: 卡片最外层必须使用 scroll-view 包裹，禁止 view
RULE: scroll-view 必须设置 height + max-height + scroll-y="true"
RULE: 高度建议 简单400-600rpx，复杂600-800rpx，数据密集800-1000rpx
RULE: 标准结构 <scroll-view><view class="card-content">内容</view></scroll-view>

=== R9: INTERACTION COMPONENTS (源码: lynx-interaction/InteractionComponents.ts) ===
RULE: movable-view,movable-area - 拖拽移动容器
RULE: functional-page-navigator - 功能页面导航器
RULE: navigator - 声明式导航，支持多种跳转方式
RULE: 交互组件必须提供视觉反馈和状态管理

=== R10: FORM COMPONENTS (源码: lynx-forms/FormComponents.ts) ===
RULE: form,input,textarea,button - 基础表单控件
RULE: checkbox-group,radio-group,picker,slider,switch - 选择控件
RULE: picker 支持 selector,multiSelector,time,date,region 类型
RULE: 表单验证必须在客户端和服务端双重验证

=== R10.1: EVENT BINDING RULES (源码: lynx-events/EventBinder.ts) ===
🚨 CRITICAL SYNTAX ERRORS (Claude4高频错误):
RULE: 禁用React语法 onClick, Vue语法 @click, HTML语法 onclick
RULE: 必用Lynx语法 bindtap,bindlongpress,bindinput,bindchange,bindscroll
RULE: 事件处理函数必须在 methods 中定义
RULE: 事件参数通过 event.detail 获取

🚨🚨🚨 CRITICAL REQUIREMENT - 事件实现强制要求 🚨🚨🚨：
**TTML中绑定的所有事件，JS中必须有对应实现**：
- TTML中有 bindtap="handleClick" → JS中必须有 handleClick() 方法
- TTML中有 bindinput="handleInput" → JS中必须有 handleInput() 方法
- TTML中有 bindchange="handleChange" → JS中必须有 handleChange() 方法
- TTML中有 bindlongpress="handleLongPress" → JS中必须有 handleLongPress() 方法
- TTML中有 bindscroll="handleScroll" → JS中必须有 handleScroll() 方法
- TTML中有 bindtouchstart="handleTouchStart" → JS中必须有 handleTouchStart() 方法
- TTML中有 bindtouchmove="handleTouchMove" → JS中必须有 handleTouchMove() 方法
- TTML中有 bindtouchend="handleTouchEnd" → JS中必须有 handleTouchEnd() 方法

🔥🔥🔥 事件实现模板 🔥🔥🔥：
```javascript
Card({
  data: {
    // 数据定义
  },
  methods: {
    // 所有TTML中绑定的事件都必须在这里实现
    handleClick(event) {
      console.log('点击事件', event.detail);
      // 事件处理逻辑
    },
    handleInput(event) {
      console.log('输入事件', event.detail.value);
      this.setData({
        inputValue: event.detail.value
      });
    },
    handleChange(event) {
      console.log('变化事件', event.detail);
      // 处理变化逻辑
    }
  }
});
```

❌ CRITICAL SYNTAX ERRORS:
❌ onClick="handleClick"       (React语法，会报错)
❌ onclick="handleClick"       (HTML语法，会报错)
❌ @click="handleClick"        (Vue语法，会报错)

✅ CORRECT SYNTAX:
bindtap="handleClick"
bindlongpress="handleLongPress"
bindinput="handleInput"
bindchange="handleChange"

=== R11: MEDIA COMPONENTS (源码: lynx-media/MediaComponents.ts) ===
RULE: image - 图片组件，支持懒加载、错误处理、多种模式
RULE: video,audio - 播放器组件，支持全屏、控制条、进度
RULE: camera,live-player,live-pusher - 实时媒体组件
RULE: 媒体组件必须处理加载失败和网络异常

=== R12: DISPLAY COMPONENTS (源码: lynx-display/DisplayComponents.ts) ===
RULE: rich-text - 富文本渲染，支持HTML子集
RULE: progress - 进度指示器，支持环形、线性样式
RULE: web-view - H5页面容器，支持与小程序通信
RULE: open-data,ad - 开放能力组件，展示用户信息和广告

=== R13: CANVAS USAGE RULES (源码: lynx-canvas/CanvasManager.ts) ===
RULE: Canvas 必须用于流程图、架构图、组织结构图等复杂图形
RULE: 每个页面应包含至少1个Canvas绘制的自定义图形
RULE: 优先使用Canvas创建动态、交互式的可视化内容
RULE: 禁止使用静态图片代替可绘制的图形

=== R14: 🚨 CANVAS INITIALIZATION RULES (源码: lynx-canvas/InitializationValidator.ts) ===
🚨 CRITICAL: Canvas初始化关键步骤防范 - 仅适用于原生Canvas

🔥 **绝对禁止Canvas和LightChart混用** 🔥
RULE: setupCanvas() 仅用于原生Canvas - 不能与LightChart混用
RULE: initChart() 仅用于LightChart - 不能与原生Canvas混用
RULE: 技术栈选择唯一 - 一个Card只能选择一种Canvas技术

✅ **原生Canvas专用 - setupCanvas()方法**：
RULE: 步骤1 lynx.createCanvasNG() - 无参数创建Canvas Element
RULE: 步骤2 addEventListener('resize') - resize事件监听必须在绑定前设置
RULE: 步骤3 SystemInfo.pixelRatio - 高分屏适配处理
RULE: 步骤4 attachToCanvasView(name) - 绑定到Canvas View

🚨 **原生Canvas初始化代码模板（禁止与LightChart混用）**：
setupCanvas() {
  const canvas = lynx.createCanvasNG();
  canvas.addEventListener("resize", ({ width, height }) => {
    canvas.width = width * SystemInfo.pixelRatio;
    canvas.height = height * SystemInfo.pixelRatio;
    const ctx = canvas.getContext('2d');
    ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
    this.canvas = canvas;
    this.ctx = ctx;
    this.canvasWidth = width;
    this.canvasHeight = height;
    this.startAnimation();
  });
  canvas.attachToCanvasView("canvas-name");
}

✅ **LightChart专用 - initChart()方法**：
initChart(e) {
  const { canvasName, width, height } = e.detail;
  this.chart = new LynxChart({ canvasName, width, height });
  this.chart.setOption(option);
}

❌ **绝对禁止的混用模式**：
RULE: 禁止 setupCanvas() + initChart() 在同一Card中
RULE: 禁止 lynx.createCanvasNG() + new LynxChart() 在同一Card中
RULE: 禁止 <canvas> + <lightcharts-canvas> 在同一TTML中
RULE: 禁止 原生Canvas API + LightChart API 混用

❌ **其他禁止的错误模式**：
RULE: 禁止 lynx.createCanvasNG("canvasName") - 不能传参数
RULE: 禁止 直接使用canvas.width/height - 没有resize监听
RULE: 禁止 忘记attachToCanvasView - Canvas不显示
RULE: 禁止 缺少pixelRatio适配 - 高分屏模糊

=== R15: CANVAS DRAWING RULES (源码: lynx-canvas/DrawingEngine.ts) ===
RULE: 基础绘图 moveTo,lineTo,rect,arc,fill,stroke
RULE: 高级特性 渐变填充,图像处理,文字渲染,阴影效果
RULE: 性能优化 离屏渲染,局部刷新,对象池,分层渲染
RULE: 交互处理 事件监听,碰撞检测,动画循环

=== R15: DATA BINDING RULES (源码: lynx-data/DataBinding.ts) ===
RULE: 所有数据绑定必须使用可选链 {{data?.property?.value}}
RULE: 数组访问必须使用可选链 {{list?.[0]?.title}}
RULE: 条件判断必须使用可选链 tt:if="{{data?.status?.loading}}"
RULE: 事件绑定必须使用可选链 bindtap="handleTap" data-id="{{item?.id}}"

=== R16: LAYOUT PATTERNS (源码: lynx-layout/LayoutPatterns.ts) ===
RULE: 响应式布局使用 view + flex 样式实现弹性布局
RULE: Grid 替代用 flex + wrap 模拟 grid 效果
RULE: 流式布局结合 scroll-view 实现长列表优化
RULE: tt:for 列表渲染必须包裹在 scroll-view 中

=== R17: COMPONENT CONSTRAINTS (源码: lynx-constraints/ComponentConstraints.ts) ===
RULE: 自闭合标签 image,input,progress,icon,checkbox,radio 必须自闭合
RULE: 容器嵌套 text 内部不能嵌套其他组件
RULE: 组件关系 swiper 必须配合 swiper-item 使用
RULE: 样式继承 大部分样式不会自动继承，需显式设置

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 组件限制 - 禁用HTML标签+必用Lynx组件+文字包裹text
RULE #2: 滚动容器 - scroll-view包裹+height设置+scroll-y配置
RULE #3: 数据绑定 - 可选链访问+XML转义+事件绑定安全
RULE #4: Canvas初始化 - 4步骤流程+resize监听+pixelRatio适配+attachToCanvasView
RULE #5: Canvas绘图 - 复杂图形+交互式内容+性能优化
RULE #6: 组件约束 - 自闭合标签+嵌套规则+样式继承
RULE #7: 事件实现 - TTML中绑定的所有事件必须在JS的methods中有对应实现
RULE #8: Web Audio API - 涉及音频时必须创建AudioContext+状态管理+资源释放

🚨 输出前强制检查 - 防止AI最高频错误 🚨：

**HTML标签混用检查 (AI最常犯错误)**：
□ 搜索代码，确认完全不存在 div 标签
□ 搜索代码，确认完全不存在 span 标签
□ 搜索代码，确认完全不存在 button 标签
□ 搜索代码，确认完全不存在 p/h1-h6 标签
□ 搜索代码，确认完全不存在 ul/li 标签
□ 搜索代码，确认完全不存在 table/tr/td 标签

**标签闭合检查 (AI最高频错误)**：
□ 确认所有 <view> 都用 </view> 闭合，绝无 </div>
□ 确认所有 <text> 都用 </text> 闭合，绝无 </div>
□ 确认所有 <scroll-view> 都用 </scroll-view> 闭合，绝无 </div>
□ 确认没有任何Lynx标签用HTML标签闭合

**组件使用检查**：
□ 确认所有文字都用 text 标签包裹
□ 确认所有容器都用 view 标签
□ 确认所有图片都用 image 标签（自闭合）
□ 确认所有交互都用 bindtap 而非 onclick

**事件实现检查 (CRITICAL)**：
□ 检查TTML中所有 bindtap 事件在JS中都有对应的方法实现
□ 检查TTML中所有 bindinput 事件在JS中都有对应的方法实现
□ 检查TTML中所有 bindchange 事件在JS中都有对应的方法实现
□ 检查TTML中所有 bindlongpress 事件在JS中都有对应的方法实现
□ 检查TTML中所有 bindscroll 事件在JS中都有对应的方法实现
□ 检查TTML中所有 bindtouchstart/move/end 事件在JS中都有对应的方法实现
□ 确认所有事件处理函数都在 methods 对象中定义

**Web Audio API 检查 (如涉及音频)**：
□ 检查是否创建了 AudioContext 对象
□ 检查是否处理了 AudioContext 状态管理
□ 检查是否实现了用户交互启动机制
□ 检查是否添加了错误处理和兼容性检查
□ 检查是否在 onUnload 中正确释放了资源

🔥🔥🔥 如发现任何上述错误，必须立即修正，绝不允许输出错误代码！
🚨 特别注意：TTML中绑定的每个事件，JS中都必须有对应实现，这是强制要求！

THESE RULES ARE MANDATORY FOR FUNCTIONAL LYNX IMPLEMENTATION
`;
