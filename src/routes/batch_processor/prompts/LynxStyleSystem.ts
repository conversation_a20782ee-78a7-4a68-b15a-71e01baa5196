export const LYNX_STYLE_SYSTEM = `🎯 LYNX TTSS 样式系统统一规范

=== 🚨 LYNX框架核心差异 (AI必读) ===
LYNX ≠ WEB：text组件与Web的div/span完全不同
- flex布局只能居中text组件容器，无法居中内部文字
- 必须使用 text-align + line-height 才能居中文字内容

=== R1: 文本居中强制规则 ===
🔥 LYNX文本居中三重保障（缺一不可）：
✅ display: flex + align-items: center + justify-content: center (容器级居中)
✅ text-align: center (文字水平居中)
✅ line-height: [height值] (文字垂直居中)

❌ AI常犯错误：
- 只用flex布局，忘记text-align + line-height
- 只用text-align，忘记line-height
- 认为Web CSS规则适用于LYNX

🔧 自动检测规则：
- 检测到text组件 → 检查是否需要居中
- 检测到border-radius: 50% → 强制添加text-align + line-height
- 检测到固定height + width → 强制添加line-height
- 检测到数字、序号、标题 → 强制添加完整居中保障

=== R2: 选择器规则 ===
支持：类型选择器(view,text) + 类选择器(.class) + ID选择器(#id)
支持：后代(.parent view) + 子元素(.parent>view) + 分组(h1,h2)
禁用：通配符(*) + 属性([attr]) + 多类(.class1.class2) + 兄弟(+~)

=== R3: 多类选择器禁用 ===
🚨 绝对禁止多类选择器 .class1.class2 (TTSS不支持)

❌ 禁止：.btn.active { } .card.selected { }
✅ 正确：.btn-active { } .card-selected { }

替代模式：.button.primary → .button-primary

=== R4: 核心特性 ===
- 无样式权重：优先级由书写顺序决定
- 无样式继承：默认不继承，可配置部分继承
- 组件样式隔离：默认作用域隔离
- 性能考量：linear/relative优于flex

=== R5: 禁用属性清单 ===
🚨 Claude4频繁违反的属性约束：

**Webkit属性**：所有 -webkit- 开头的属性
**现代CSS**：backdrop-filter, filter, clip-path, mask, object-fit, user-select, cursor, resize, appearance, outline
**Grid布局**：display: grid, grid-template-*, grid-column, grid-row, grid-area, grid-gap, grid-auto-*
**阴影效果**：text-shadow, drop-shadow (box-shadow部分支持)
**选择器**：.class1.class2, [attr=value], *, element+element, element~element
**定位限制**：position: absolute (禁用，使用relative/fixed替代)
**滚动控制**：scroll-behavior, overscroll-behavior, scroll-snap-type
**交互控制**：pointer-events, user-select, cursor

Claude4最常犯错误：
1. -webkit-backdrop-filter (出现频率极高)
2. backdrop-filter (背景模糊)
3. filter (CSS滤镜)
4. display: grid (Grid布局)
5. position: absolute (定位限制)
6. user-select (文本选择控制)

=== R6: 支持属性清单 ===
**布局**：display(block/inline/flex), position(static/relative/fixed), float, overflow, visibility, opacity
**尺寸**：width, height, max-*, min-*, box-sizing
**边距边框**：margin, padding, border, border-radius
**文字**：font-*, color, text-align, line-height, letter-spacing, text-overflow
**背景**：background-*, background-color, background-image, background-size, background-repeat, background-position
**Flex**：flex-*, justify-content, align-items, flex-direction, flex-wrap, align-content, align-self
**定位**：top, right, bottom, left, z-index (仅配合relative/fixed使用)
**动画**：transition, animation, transform(2D变换), transform-origin

🚨 **重要限制说明**：
- position: absolute 禁用，使用 relative 或 fixed 替代
- 复杂 box-shadow 可能有兼容性问题，建议简化
- 所有 -webkit- 前缀属性完全禁用

=== R7: 关键约束规则 ===
**Webkit前缀**：禁用所有 -webkit- 开头的属性
**现代CSS**：禁用 backdrop-filter, filter, mask, clip-path
**Grid布局**：禁用，使用 flex + flex-wrap 替代
**单位系统**：优先 rpx，可用 px/em/rem，禁用 vw/vh
**颜色系统**：确保对比度，禁用同色/相近色

=== R8: 验证清单 ===
生成代码前必须检查：
□ 无webkit前缀属性 □ 无现代CSS特性 □ 无Grid布局
□ 无text-shadow □ 无用户交互控制属性 □ 优先使用rpx
□ 颜色对比度足够 □ 无多类选择器 □ 文本居中完整

=== R9: 降级策略 ===
**背景模糊**：backdrop-filter → background-color: rgba()
**滤镜效果**：filter → opacity
**Grid布局**：display: grid → display: flex; flex-wrap: wrap
**文字阴影**：text-shadow → background-color + padding

=== R10: 移动端表格排版规则 ===
🚨 **Table布局移动端适配强制要求**：

**列宽控制**：
- 表格总宽度不超过 750rpx (屏幕宽度)
- 列宽使用百分比或固定rpx，避免内容溢出
- 重要列(如标题)最小宽度 120rpx，次要列可压缩至 80rpx
- 超过4列的表格必须支持横向滚动

**文字换行**：
- 表格单元格必须设置 word-wrap: break-word
- 长文本使用 text-overflow: ellipsis + overflow: hidden
- 单元格内容超过2行时显示省略号
- 数字和日期类型禁止换行，使用固定宽度

**移动端优化**：
- 表头固定：position: sticky; top: 0
- 行高最小 88rpx，确保触摸友好
- 单元格padding: 16rpx 12rpx，保证可读性
- 斑马纹背景提升可读性：nth-child(even)

**响应式处理**：
- 小屏幕(<600rpx)时考虑卡片式布局替代表格
- 关键信息前置，次要信息可隐藏或折叠
- 提供表格/卡片视图切换选项

**交互优化**：
- 可排序列添加视觉指示器
- 选中行高亮显示
- 支持长按多选操作
- 横向滚动时保持首列固定

=== R11: 颜色对比度规则 ===
🚨 **绝对禁止同色**：文字color和背景background-color严禁相同
**对比度标准**：文字背景对比度≥4.5:1，重要文字≥7:1
**常见错误**：白色文字+白色背景、黑色文字+黑色背景

❌ 错误：.text { color: #fff; background: #fff; }
✅ 正确：.text { color: #333; background: #fff; }

=== R12: 强制执行规则 ===
🚨 **违反约束的后果**：
违反任何上述规则的TTSS代码都是错误的，会导致：
1. 样式无法生效
2. 渲染异常
3. 性能问题
4. 跨平台兼容性问题

**必须严格遵守TTSS子集约束，没有例外！**

🔧 **AI生成代码自检清单**：
生成代码后必须自动检查：
□ 文本居中是否使用了三重保障
□ 是否误用了禁用属性
□ position: absolute 是否已替换为 relative/fixed
□ 是否存在多类选择器
□ 颜色对比度是否充足
□ 表格布局是否适配移动端
`;

export default LYNX_STYLE_SYSTEM;
