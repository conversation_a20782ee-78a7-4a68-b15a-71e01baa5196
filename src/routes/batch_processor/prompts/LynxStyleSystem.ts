export const LYNX_STYLE_SYSTEM = `🎯 LYNX TTSS 样式系统统一规范 (SOURCE-CODE VERIFIED)

🚨🚨🚨 LYNX框架特殊警告 - AI必读！！！ 🚨🚨🚨

🔥🔥🔥 LYNX ≠ WEB！AI必须理解框架差异！🔥🔥🔥
⚠️ LYNX的text组件与Web的div/span完全不同！
⚠️ flex布局只能居中text组件容器，无法居中内部文字！
⚠️ 必须使用 text-align + line-height 才能居中文字内容！
⚠️ 这不是CSS标准，这是LYNX框架的特殊设计！

=== R1: LYNX文本居中强制规则 (AI最常犯错误) ===
🚨🚨🚨 LYNX文本居中致命错误（AI最常犯，100%出错率）🚨🚨🚨

🔥🔥🔥 LYNX框架特殊性 - 为什么FLEX不够用 🔥🔥🔥：
⚠️ LYNX的text组件与Web不同：flex布局只能居中容器，无法居中text组件内部的文字内容！
⚠️ text组件内的文字需要额外的text-align和line-height才能真正居中！
⚠️ 这是LYNX框架的特殊限制，AI必须理解并遵守！

❌ AI常犯的致命错误模式：
❌ 错误模式1: 只写 display: flex + align-items: center + justify-content: center (容器居中了，但文字没居中！)
❌ 错误模式2: 只写 text-align: center 而忘记 line-height (只有水平居中，没有垂直居中！)
❌ 错误模式3: 认为flex就够了，不添加text-align和line-height (完全错误的理解！)

🔥🔥🔥 LYNX文本居中强制三重保障（缺一不可）🔥🔥🔥：
✅ display: flex + align-items: center + justify-content: center  (容器级居中)
✅ text-align: center  (文字水平居中，LYNX text组件必需！)
✅ line-height: [height值]  (文字垂直居中，LYNX text组件必需！)

🚨🚨🚨 LYNX框架核心理解 - AI必须牢记 🚨🚨🚨：
🔴 FLEX布局 = 只能居中text组件容器本身
🔴 text-align + line-height = 才能居中text组件内部的文字内容
🔴 两者缺一不可！LYNX框架的text组件就是这样设计的！
🔴 AI不能用Web思维理解LYNX，必须遵循LYNX的特殊规则！

🔧 LYNX文本居中自动检测规则（AI必须执行）：
🚨 检测到任何text组件 → 必须检查是否需要居中 → 如需居中必须添加text-align + line-height
🚨 检测到 border-radius: 50% → 强制添加 text-align: center + line-height: [height值]
🚨 检测到固定 height + width → 强制添加 line-height: [height值]
🚨 检测到 flex 居中 → 强制额外添加 text-align: center + line-height: [height值]
🚨 检测到数字、序号、标题 → 强制添加完整的三重居中保障

⚠️ AI生成代码时必须自动执行这些检测，不能遗漏任何一个！

=== R2: SELECTOR RULES (源码: lynx-styles/SelectorValidator.ts) ===
RULE: 支持选择器 类型选择器view,text + 类选择器.class + ID选择器#id
RULE: 有限高级选择器 后代.parent view + 子元素.parent>view + 分组h1,h2
RULE: 禁用选择器 通配符*,属性[attr],多类.class1.class2,兄弟+~
RULE: 伪类伪元素 仅支持:not(性能影响) + text组件伪元素(需配置)

=== R3: MULTI-CLASS ABSOLUTE PROHIBITION (源码: lynx-styles/MultiClassBlocker.ts) ===
🚨 CRITICAL: 多类选择器绝对禁用 (TTSS核心语法限制)
RULE: 绝对禁止多类选择器 .class1.class2 - TTSS不支持此语法
RULE: 思考前必须检查 设计样式时避免多类选择器思维
RULE: 思考后必须验证 完成代码后检查是否误用多类选择器
RULE: 强制单类替代 所有多类需求必须合并为单一类名

❌ 绝对禁止示例:
.btn.active { } /* 致命错误：TTSS不支持 */
.card.selected { } /* 致命错误：TTSS不支持 */
.item.hover.focus { } /* 致命错误：TTSS不支持 */

✅ 强制替代方案:
.btn-active { } /* 正确：单一类名 */
.card-selected { } /* 正确：单一类名 */
.item-hover-focus { } /* 正确：单一类名 */

RULE: 替代模式规范 .button.primary → .button-primary

=== R4: CORE FEATURES (源码: lynx-styles/CoreFeatures.ts) ===
RULE: 无样式权重 优先级由书写顺序决定，后者覆盖前者
RULE: 无样式继承 默认不继承，可配置enableCSSInheritance部分继承
RULE: 组件样式隔离 默认作用域隔离，仅对组件内部生效
RULE: 性能考量 linear/relative优于flex，避免:not伪类

=== R5: FORBIDDEN CSS PROPERTIES (源码: lynx-styles/ForbiddenProperties.ts) ===
🚨 CRITICAL WARNING: Claude4频繁违反TTSS CSS属性约束，以下规则必须严格执行

RULE: 禁用Webkit私有属性 -webkit-backdrop-filter,-webkit-*全部不支持
RULE: 禁用现代CSS高级属性 backdrop-filter,filter,clip-path,mask,object-fit
RULE: 禁用Grid布局 display:grid,grid-template-*,grid-column,grid-row全部禁止
RULE: 禁用CSS阴影效果 text-shadow,drop-shadow禁止，box-shadow部分支持

🚨 ABSOLUTELY FORBIDDEN CSS PROPERTIES - 绝对禁止的CSS属性：

Webkit属性禁用清单：
- 所有以 -webkit- 开头的属性都禁止使用

现代CSS高级属性禁用清单 - TTSS作为CSS子集不支持：
- backdrop-filter - 禁止！模糊背景效果不支持
- filter - 禁止！CSS滤镜不支持
- clip-path - 禁止！裁剪路径不支持
- mask - 禁止！遮罩效果不支持
- object-fit - 禁止！对象适配不支持
- object-position - 禁止！对象定位不支持
- user-select - 禁止！用户选择控制不支持
- pointer-events - 禁止！指针事件控制不支持
- cursor - 禁止！鼠标样式不支持
- resize - 禁止！元素调整大小不支持
- appearance - 禁止！外观控制不支持
- outline - 禁止！轮廓线不支持
- scroll-behavior - 禁止！滚动行为不支持
- overscroll-behavior - 禁止！过度滚动不支持
- scroll-snap-type - 禁止！滚动对齐不支持

Grid布局禁用清单 - TTSS不支持Grid：
- display: grid - 禁止！使用flexbox布局
- grid-template-columns - 禁止！
- grid-template-rows - 禁止！
- grid-template-areas - 禁止！
- grid-column - 禁止！
- grid-row - 禁止！
- grid-area - 禁止！
- grid-gap - 禁止！
- grid-auto-flow - 禁止！
- grid-auto-columns - 禁止！
- grid-auto-rows - 禁止！

CSS选择器禁用清单 - TTSS选择器限制：
- .class1.class2 - 禁止！多类选择器不支持
- [attr=value] - 禁止！属性选择器不支持
- * - 禁止！通配符选择器不支持
- element + element - 禁止！相邻兄弟选择器不支持
- element ~ element - 禁止！通用兄弟选择器不支持

CSS阴影和效果禁用清单：
- text-shadow - 禁止！文本阴影不支持
- box-shadow - 部分支持，但复杂阴影可能有问题
- drop-shadow - 禁止！投影效果不支持

CLAUDE4 最常犯的CSS错误属性：
1. -webkit-backdrop-filter - 出现频率极高
2. backdrop-filter - 背景模糊效果
3. filter - CSS滤镜效果
4. display: grid - Grid布局
5. user-select - 文本选择控制
6. cursor - 鼠标样式

=== R6: SUPPORTED CSS PROPERTIES (源码: lynx-styles/SupportedProperties.ts) ===
🚨 ABSOLUTELY REQUIRED CSS PROPERTIES - 必须使用的正确属性

TTSS完全支持的CSS属性清单：

布局相关：
- display: block, inline, inline-block, flex, none
- position: static, relative, absolute, fixed
- float: left, right, none
- clear: left, right, both, none
- overflow: visible, hidden, scroll, auto
- overflow-x, overflow-y
- visibility: visible, hidden
- opacity: 0-1数值

尺寸相关：
- width, height
- max-width, max-height
- min-width, min-height
- box-sizing: content-box, border-box

边距边框：
- margin, margin-top, margin-right, margin-bottom, margin-left
- padding, padding-top, padding-right, padding-bottom, padding-left
- border, border-width, border-style, border-color
- border-top, border-right, border-bottom, border-left
- border-radius

文字样式：
- font, font-family, font-size, font-weight, font-style
- color
- text-align: left, center, right, justify
- text-decoration: none, underline, line-through
- line-height
- letter-spacing
- text-overflow: clip, ellipsis

背景相关：
- background, background-color
- background-image
- background-repeat: repeat, no-repeat, repeat-x, repeat-y
- background-position
- background-size: auto, cover, contain, 具体尺寸

Flexbox布局：
- flex, flex-grow, flex-shrink, flex-basis
- flex-direction: row, column, row-reverse, column-reverse
- flex-wrap: nowrap, wrap, wrap-reverse
- justify-content: flex-start, flex-end, center, space-between, space-around
- align-items: flex-start, flex-end, center, stretch, baseline
- align-content: flex-start, flex-end, center, stretch, space-between, space-around
- align-self: auto, flex-start, flex-end, center, baseline, stretch

定位相关：
- top, right, bottom, left
- z-index

动画过渡：
- transition, transition-property, transition-duration, transition-timing-function, transition-delay
- animation, animation-name, animation-duration, animation-timing-function, animation-delay, animation-iteration-count, animation-direction, animation-fill-mode
- transform: translate, rotate, scale, skew等2D变换
- transform-origin

=== R7: CRITICAL CLAUDE4 RULES (源码: lynx-styles/Claude4Rules.ts) ===
🚨 CRITICAL: 样式系统关键约束规则 (重要技术限制)

Rule 1: Webkit前缀强制禁用
- 任何以 -webkit- 开头的属性都禁止使用
- 使用标准CSS属性替代
- 特别禁止 -webkit-backdrop-filter

Rule 2: 现代CSS特性强制禁用
- backdrop-filter, filter, mask, clip-path 完全禁止
- Grid布局完全禁用，使用Flexbox替代
- 所有用户交互控制属性禁止（user-select, cursor, pointer-events）

Rule 3: 单位系统强制要求
- 优先使用 rpx 单位（750rpx = 屏幕宽度）
- 可以使用 px, %, em, rem
- 禁止使用 vw, vh, vmin, vmax

Rule 4: 颜色系统强制要求
- 必须为文字和背景设置对比度足够的颜色
- 严禁使用同色或相近色
- 支持 hex, rgb, rgba, 预定义颜色名

=== R8: VALIDATION CHECKLIST - 验证清单 ===
在生成TTSS代码前，必须检查：
□ 没有使用任何webkit前缀属性
□ 没有使用 backdrop-filter, filter, mask, clip-path
□ 没有使用Grid布局相关属性
□ 没有使用text-shadow
□ 没有使用user-select, cursor, pointer-events
□ 单位优先使用rpx
□ 颜色对比度足够
□ 只使用TTSS支持的属性清单中的属性
□ 没有使用多类选择器 .class1.class2
□ 文本居中使用了 text-align + line-height 组合

=== R9: FALLBACK STRATEGIES - 降级策略 ===
当需要被禁止的效果时，使用以下替代方案：

背景模糊效果替代：
FORBIDDEN: backdrop-filter: blur(10px);
ALTERNATIVE: background-color: rgba(255, 255, 255, 0.8);

滤镜效果替代：
FORBIDDEN: filter: brightness(0.8);
ALTERNATIVE: opacity: 0.8;

Grid布局替代：
FORBIDDEN: display: grid;
ALTERNATIVE: display: flex; flex-wrap: wrap;

文字阴影替代：
FORBIDDEN: text-shadow: 2px 2px 4px black;
ALTERNATIVE: background-color: rgba(0,0,0,0.1); padding: 4rpx 8rpx;

=== R8: COLOR CONTRAST CRITICAL RULES (源码: lynx-styles/ColorContrastValidator.ts) ===
🚨 CRITICAL: 颜色对比度强制检查 (防止同色bug导致文字不可见)
RULE: 绝对禁止同色 文字color和容器background-color严禁使用相同颜色值
RULE: 对比度最低标准 文字和背景对比度必须≥4.5:1，重要文字≥7:1
RULE: 思考前检查 设计颜色方案时必须预先检查对比度是否充足
RULE: 思考后验证 完成样式后必须再次验证是否存在同色bug
RULE: 常见同色陷阱 白色文字+白色背景、黑色文字+黑色背景、透明色重叠

❌ 严重错误示例 (同色bug):
.container { background-color: #ffffff; }
.text { color: #ffffff; } /* 致命错误：白色文字+白色背景 */

.dark-box { background-color: #000000; }
.dark-text { color: #000000; } /* 致命错误：黑色文字+黑色背景 */

.blue-card { background-color: #1976d2; }
.blue-title { color: #1976d2; } /* 致命错误：蓝色文字+蓝色背景 */

✅ 正确对比度示例:
.light-container { background-color: #ffffff; }
.dark-text { color: #333333; } /* 正确：深色文字+浅色背景 */

.dark-container { background-color: #333333; }
.light-text { color: #ffffff; } /* 正确：浅色文字+深色背景 */

.blue-container { background-color: #1976d2; }
.white-text { color: #ffffff; } /* 正确：白色文字+蓝色背景 */

RULE: 颜色格式支持 hex,rgb,rgba,预定义颜色名
RULE: 动态颜色检查 主题切换时必须重新验证对比度

=== R9: ICON SYSTEM RULES (源码: lynx-icons/IconSystem.ts) ===
RULE: 唯一图标方案 必须且只能使用Font Awesome图标系统
RULE: 禁止Emoji 绝对禁止Unicode Emoji字符
RULE: 禁止替代方案 严禁SVG,PNG,JPG或其他字体图标库

=== R10: TIMELINE PRECISION ALIGNMENT RULES (源码: lynx-styles/TimelineAlignmentValidator.ts) ===
🚨 CRITICAL: 时间轴组件精准对齐约束 (防止样式错乱和对齐偏差)
RULE: 时间轴容器定位一致性 timeline-line和timeline-node必须使用相同定位方式
RULE: 禁止混合定位 timeline-line使用absolute时，timeline-node不能使用flex
RULE: 推荐方案A 全部使用relative定位 + flex布局实现时间轴
RULE: 推荐方案B 全部使用absolute定位 + 精确计算位置
RULE: 错误后果 定位不一致导致节点脱离时间轴，随页面滚动产生错位

🎯 CRITICAL: 时间轴精准对齐强制要求 (像素级精确)
RULE: Icon与Line精准对齐 时间轴icon必须与timeline-line垂直居中对齐，误差≤2rpx
RULE: 序号与背景精准对齐 序号数字必须在背景圆形中完美居中，水平垂直都居中
RULE: 多元素统一对齐 icon、line、序号、背景色必须在同一水平线上精准对齐
RULE: 响应式对齐保持 不同屏幕尺寸下对齐精度必须保持一致
RULE: 滚动对齐稳定 页面滚动时所有时间轴元素必须保持相对位置不变

🚨 CRITICAL: 时间轴CSS样式约束 (防止换行和对齐问题)
RULE: 强制单行显示 white-space:nowrap + overflow:hidden + text-overflow:ellipsis
RULE: 固定宽度策略 max-width限制 + min-width保证 + flex-shrink:0防压缩
RULE: 精确定位计算 使用数学计算确保像素级对齐，避免视觉估算
RULE: 响应式单位 统一使用rpx确保不同屏幕下的一致性表现

❌ 错误示例 (定位不一致+对齐偏差+换行问题):
.timeline-line { position: absolute; left: 50%; }
.timeline-node { display: flex; align-items: center; } /* 节点会脱离时间轴 */
.timeline-icon { margin-top: 5rpx; } /* 错误：icon与line不对齐 */
.timeline-number { text-align: left; } /* 错误：序号未居中 */
.timeline-title { width: auto; } /* 错误：可能导致换行 */
.timeline-year { display: inline; } /* 错误：年代标签可能换行 */

✅ 正确示例A (统一relative+精准对齐+换行控制):
.timeline-container { display: flex; flex-direction: column; position: relative; }
.timeline-line {
  position: absolute; left: 60rpx; top: 0; bottom: 0;
  width: 4rpx; background: #e0e0e0;
}
.timeline-node {
  display: flex; align-items: center; position: relative;
  padding-left: 100rpx; margin: 40rpx 0;
}
.timeline-icon {
  position: absolute; left: 42rpx; /* 精确计算：60rpx - 18rpx/2 = 51rpx */
  width: 36rpx; height: 36rpx;
  display: flex; align-items: center; justify-content: center;
  background: #1976d2; border-radius: 50%; flex-shrink: 0;
}
.timeline-number {
  color: white; font-size: 24rpx;
  text-align: center; line-height: 36rpx; /* 与icon高度一致 */
}
.timeline-content {
  flex: 1; min-width: 0; /* 防止内容撑开容器 */
}
.timeline-title {
  white-space: nowrap; /* 强制单行显示 */
  overflow: hidden; text-overflow: ellipsis;
  max-width: 200rpx; /* 固定最大宽度 */
}
.timeline-year {
  white-space: nowrap; /* 年代标签禁止换行 */
  display: inline-block; min-width: 80rpx;
}

✅ 正确示例B (统一absolute+像素级精确):
.timeline-line { position: absolute; left: 60rpx; width: 4rpx; }
.timeline-node { position: absolute; left: 100rpx; }
.timeline-icon {
  position: absolute; left: 42rpx; /* 精准对齐：line中心 - icon半径 */
  width: 36rpx; height: 36rpx; border-radius: 18rpx;
  display: flex; align-items: center; justify-content: center;
}
.timeline-number {
  width: 100%; height: 100%;
  display: flex; align-items: center; justify-content: center;
  font-size: 24rpx; color: white;
}

=== R11: FALLBACK STRATEGIES (源码: lynx-styles/FallbackStrategies.ts) ===
RULE: 背景模糊替代 backdrop-filter:blur() → background-color:rgba()
RULE: 滤镜效果替代 filter:brightness() → opacity
RULE: Grid布局替代 display:grid → display:flex + flex-wrap:wrap
RULE: 文字阴影替代 text-shadow → background-color + padding

=== MANDATORY IMPLEMENTATION RULES ===
RULE #1: 多类选择器绝对禁用 - .class1.class2严禁使用+思考前后双重检查+强制单类替代
RULE #2: CSS属性约束 - 禁用webkit前缀+现代CSS特性+Grid布局
RULE #3: 颜色对比度强制检查 - 禁止同色bug+思考前后双重验证+对比度≥4.5:1
RULE #4: 时间轴精准对齐 - icon与line精确对齐+序号与背景完美居中+像素级精度
RULE #5: 时间轴排版控制 - 关键文本单行显示+溢出省略处理+容器分离布局
RULE #6: 定位一致性约束 - 时间轴组件统一定位方式+防止节点脱离
RULE #7: 降级策略 - 禁用效果的替代方案+兼容性保证

THESE RULES ARE MANDATORY FOR FUNCTIONAL TTSS IMPLEMENTATION
`;
